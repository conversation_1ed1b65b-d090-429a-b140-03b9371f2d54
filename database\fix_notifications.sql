-- Fix notification table structure and ensure consistency
-- This script ensures the notifications table has the correct structure

-- Check if notifications table exists and has correct structure
-- If it has 'id' as primary key, we need to update references to use 'notification_id'

-- First, let's check the current structure
DESCRIBE notifications;

-- If the table uses 'id' as primary key, we need to rename it to 'notification_id'
-- This is a safe operation that maintains data integrity

-- Step 1: Check if we need to rename the primary key column
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'notifications'
    AND COLUMN_NAME = 'id'
    AND COLUMN_KEY = 'PRI'
);

-- Step 2: If 'id' exists as primary key, rename it to 'notification_id'
SET @sql = IF(@column_exists > 0,
    'ALTER TABLE notifications CHANGE COLUMN id notification_id INT AUTO_INCREMENT',
    'SELECT "Primary key already named notification_id" as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 3: Ensure the table has all required columns with correct types
ALTER TABLE notifications 
MODIFY COLUMN notification_id INT AUTO_INCREMENT PRIMARY KEY,
MODIFY COLUMN user_id INT NOT NULL,
MODIFY COLUMN title VARCHAR(255) NOT NULL,
MODIFY COLUMN message TEXT NOT NULL,
MODIFY COLUMN type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
MODIFY COLUMN is_read TINYINT(1) DEFAULT 0,
MODIFY COLUMN link VARCHAR(255) DEFAULT NULL,
MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Step 4: Ensure proper indexes exist
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

-- Step 5: Ensure foreign key constraint exists (if users table exists)
SET @fk_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'notifications'
    AND CONSTRAINT_NAME LIKE '%fk%'
    AND REFERENCED_TABLE_NAME = 'users'
);

-- Add foreign key if it doesn't exist and users table exists
SET @users_table_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'users'
);

SET @add_fk_sql = IF(@fk_exists = 0 AND @users_table_exists > 0,
    'ALTER TABLE notifications ADD CONSTRAINT fk_notifications_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE',
    'SELECT "Foreign key already exists or users table not found" as status'
);

PREPARE stmt FROM @add_fk_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Display final table structure
DESCRIBE notifications;

SELECT 'Notification table structure has been updated successfully!' as result;
